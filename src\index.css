@import "tailwindcss";

:root {
  --btn-gradient: radial-gradient(
    96.2% 79.04% at 50% 130.56%,
    #47daff 0%,
    #0067f7 100%
  );
}

body {
  font-family: "IBM Plex Sans Arabic", sans-serif;
}

.btn-gradient {
  background: var(--btn-gradient);
}

.custom-shadow {
  box-shadow: 0px 0px 0px 2px #20346014, 0px 1px 2px 0px #2034601f,
    inset 0px 0px 0px 8px #ffffff;
}

.x-container {
  width: 1076px;
  margin: 0 auto;
}

* {
  box-sizing: border-box;
}

body {
  font-family: "IBM Plex Sans Arabic", Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  text-align: right;

}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Smooth transitions */
* {
  transition: all 0.2s ease-in-out;
}

select option {
  font-size: 13px;
  font-weight: 500;
  color: #98a2b3;
}
