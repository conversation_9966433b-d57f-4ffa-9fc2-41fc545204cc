import { useEffect } from "react";
import closeBtn from '../assets/IconButton.png'
import FileUpload from "./FileUpload";
export default function AddChildSideBar({isOpen , onClose}) {
useEffect(() => {
    function handleKey(e) {
      if (e.key === "Escape") onClose();
    }
    if (isOpen) window.addEventListener("keydown", handleKey);
    return () => window.removeEventListener("keydown", handleKey);
  }, [isOpen, onClose]);

  // determine classes based on side


  return (
    // wrapper full-screen لعمل overlay واحتواء السايدبار
    <div
      className={`fixed inset-0 z-50 pointer-events-none`}
      aria-hidden={!isOpen}
    >
      {/* Overlay */}
      <div
        onClick={onClose}
        className={`absolute inset-0 bg-black transition-opacity duration-300 ${
          isOpen ? "opacity-50 pointer-events-auto" : "opacity-0"
        }`}
        aria-hidden="true"
      />

      {/* Sidebar panel */}
      <div
        role="dialog"
        aria-modal="true"
        className={`fixed top-0 left-0 h-full w-[500px] p-4 bg-white shadow-xl transform transition-transform duration-300 pointer-events-auto
          ${isOpen ? 'translate-x-0' : '-translate-x-full'}`}
      >
        {/* محتوى السايدبار */}
        <div className="p-4 h-full flex flex-col">
          <div className="flex bg-[#F9FAFB] p-5 items-center justify-between ">
            <p className="text-lg font-semibold">إضافة طفل جديد</p>
            <button
              onClick={onClose}
              aria-label="Close sidebar"
            >
              <img src={closeBtn} alt="" />
            </button>
          </div>
          <div className=" my-6 rounded-xl border border-[#E4E7EC] p-4">
            <p className=" rounded-lg bg-[#F9FAFB] mb-4 p-3 text-lg font-medium">
              الصورة الشخصية
            </p>
            <FileUpload/>
          </div>

          <div className="mt-4">
          
          </div>
        </div>
      </div>
    </div>
  );
}


