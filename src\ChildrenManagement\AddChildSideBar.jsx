import { useEffect, useState } from "react";
import closeBtn from '../assets/IconButton.png'
import FileUpload from "./FileUpload";
import { FiChevronUp, FiChevronDown } from 'react-icons/fi';
export default function AddChildSideBar({isOpen , onClose}) {
  const [childInfoExpanded, setChildInfoExpanded] = useState(true);
  const [additionalInfoExpanded, setAdditionalInfoExpanded] = useState(true);
  const [formData, setFormData] = useState({
    name: '',
    gender: '',
    birthDate: '',
    position: '',
    mentalState: '',
    notes: ''
  });

  useEffect(() => {
    function handleKey(e) {
      if (e.key === "Escape") onClose();
    }
    if (isOpen) window.addEventListener("keydown", handleKey);
    return () => window.removeEventListener("keydown", handleKey);
  }, [isOpen, onClose]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = () => {
    // هنا يمكن إضافة منطق حفظ البيانات
    console.log('Child data:', formData);
    onClose();
  };

  // determine classes based on side


  return (
    // wrapper full-screen لعمل overlay واحتواء السايدبار
    <div
      className={`fixed inset-0 z-50 pointer-events-none`}
      aria-hidden={!isOpen}
    >
      {/* Overlay */}
      <div
        onClick={onClose}
        className={`absolute inset-0 bg-black transition-opacity duration-300 ${
          isOpen ? "opacity-50 pointer-events-auto" : "opacity-0"
        }`}
        aria-hidden="true"
      />

      {/* Sidebar panel */}
      <div
        role="dialog"
        aria-modal="true"
        className={`fixed top-0 left-0 h-full w-[500px] p-4 bg-white shadow-xl transform transition-transform duration-300 pointer-events-auto
          ${isOpen ? 'translate-x-0' : '-translate-x-full'}`}
      >
        {/* محتوى السايدبار */}
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="flex bg-[#F9FAFB] p-5 items-center justify-between">
            <p className="text-lg font-semibold">إضافة طفل جديد</p>
            <button
              onClick={onClose}
              aria-label="Close sidebar"
              className="p-1 hover:bg-gray-200 rounded"
            >
              <img src={closeBtn} alt="" />
            </button>
          </div>

          {/* Scrollable Content */}
          <div className="flex-1 overflow-y-auto p-4 space-y-6">

            {/* الصورة الشخصية */}
            <div className="rounded-xl border border-[#E4E7EC] p-4">
              <p className="rounded-lg bg-[#F9FAFB] mb-4 p-3 text-lg font-medium">
                الصورة الشخصية
              </p>
              <FileUpload/>
            </div>

            {/* معلومات الطفل */}
            <div className="rounded-xl border border-[#E4E7EC] p-4">
              <button
                onClick={() => setChildInfoExpanded(!childInfoExpanded)}
                className="w-full flex items-center justify-between p-3 bg-[#F9FAFB] rounded-lg mb-4"
              >
                <span className="text-lg font-medium">معلومات الطفل</span>
                {childInfoExpanded ? (
                  <FiChevronUp className="w-5 h-5" />
                ) : (
                  <FiChevronDown className="w-5 h-5" />
                )}
              </button>

              {childInfoExpanded && (
                <div className="space-y-4">
                  {/* الاسم الكامل */}
                  <div>
                    <input
                      type="text"
                      placeholder="الاسم الكامل"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className="w-full p-3 border border-[#E4E7EC] rounded-lg text-right placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  {/* النوع */}
                  <div>
                    <select
                      value={formData.gender}
                      onChange={(e) => handleInputChange('gender', e.target.value)}
                      className="w-full p-3 border border-[#E4E7EC] rounded-lg text-right focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">النوع (ذكر / أنثى)</option>
                      <option value="male">ذكر</option>
                      <option value="female">أنثى</option>
                    </select>
                  </div>

                  {/* تاريخ الميلاد */}
                  <div>
                    <input
                      type="date"
                      value={formData.birthDate}
                      onChange={(e) => handleInputChange('birthDate', e.target.value)}
                      className="w-full p-3 border border-[#E4E7EC] rounded-lg text-right focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="تاريخ الميلاد / الشهر"
                    />
                  </div>

                  {/* المرتبة المسؤول */}
                  <div>
                    <select
                      value={formData.position}
                      onChange={(e) => handleInputChange('position', e.target.value)}
                      className="w-full p-3 border border-[#E4E7EC] rounded-lg text-right focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">المرتبة المسؤول</option>
                      <option value="first">الأول</option>
                      <option value="second">الثاني</option>
                      <option value="third">الثالث</option>
                      <option value="fourth">الرابع</option>
                      <option value="fifth">الخامس</option>
                    </select>
                  </div>

                  {/* الوضع النفسي الحالي */}
                  <div>
                    <select
                      value={formData.mentalState}
                      onChange={(e) => handleInputChange('mentalState', e.target.value)}
                      className="w-full p-3 border border-[#E4E7EC] rounded-lg text-right focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">الوضع النفسي الحالي</option>
                      <option value="قلق">قلق</option>
                      <option value="ضغط نفسي">ضغط نفسي</option>
                      <option value="اجهاد">اجهاد</option>
                      <option value="طبيعي">طبيعي</option>
                    </select>
                  </div>
                </div>
              )}
            </div>

            {/* معلومات إضافية */}
            <div className="rounded-xl border border-[#E4E7EC] p-4">
              <button
                onClick={() => setAdditionalInfoExpanded(!additionalInfoExpanded)}
                className="w-full flex items-center justify-between p-3 bg-[#F9FAFB] rounded-lg mb-4"
              >
                <span className="text-lg font-medium">معلومات إضافية (اختيارية)</span>
                {additionalInfoExpanded ? (
                  <FiChevronUp className="w-5 h-5" />
                ) : (
                  <FiChevronDown className="w-5 h-5" />
                )}
              </button>

              {additionalInfoExpanded && (
                <div>
                  <textarea
                    placeholder="ملاحظات أولية"
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    rows={4}
                    className="w-full p-3 border border-[#E4E7EC] rounded-lg text-right placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                  />
                </div>
              )}
            </div>
          </div>

          {/* Footer Buttons */}
          <div className="p-4 border-t border-[#E4E7EC] flex gap-3">
            <button
              onClick={onClose}
              className="flex-1 px-4 py-3 border border-[#E4E7EC] text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              إلغاء
            </button>
            <button
              onClick={handleSubmit}
              className="flex-1 px-4 py-3 bg-[#007AFF] text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center justify-center gap-2"
            >
              <span>+</span>
              إضافة الطفل
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}


