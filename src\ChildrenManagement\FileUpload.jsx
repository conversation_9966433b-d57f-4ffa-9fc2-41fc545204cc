import { useState } from "react";
import Upload  from "../assets/arrow-down-tray.png"; // أيقونة من lucide-react
import loading from '../assets/spinner.png'
export default function FileUpload() {
  const [fileName, setFileName] = useState(null);

  const handleFileChange = (e) => {
    if (e.target.files && e.target.files.length > 0) {
      setFileName(e.target.files[0]);
    }
  };

  return (
    <div className="w-full  max-w-md mx-auto">
      <label
        htmlFor="file-upload"
        className="flex bg-[#F9FAFB] flex-col items-center justify-center w-full  py-6 px-8 border-2 border-dashed border-[#E4E7EC] rounded-lg cursor-pointer hover:bg-gray-50 transition"
      >
        <div className=" mb-2 flex-row-reverse gap-2 flex justify-center items-center">
            <span className="text-[13px] font-semibold text-[#667085]">Import Files</span>
            <img src={Upload} alt="" />
        </div>

        <span className="text-xs text-gray-500">
          Drag and drop files here or click to upload
        </span>
        <input
          id="file-upload"
          type="file"
          className="hidden"
          onChange={handleFileChange}
        />
      </label>
        {fileName && (
            <div className="mt-4 flex bg-[#F9FAFB] items-center border-[#E4E7EC] justify-between px-3 py-3.5 border rounded-lg  ">
                {/* صورة مصغرة للملف */}
                <div className="flex items-center space-x-3">
                    {fileName.type.startsWith("image/") ? (
                    <img
                        src={URL.createObjectURL(fileName)}
                        alt={fileName.name}
                        className="w-10 h-10 object-cover rounded"
                    />
                    ) : (
                    <div className="w-10 h-10 flex items-center justify-center bg-gray-100 rounded">
                        <img src={Upload} alt="" />
                    </div>
                    )}
                    <div>
                    <p className="text-sm font-medium text-gray-800">{fileName.name}</p>
                    <p dir="ltr" className="text-xs text-blue-600">Preprocessing...</p>
                    </div>
                </div>

                <button
            
                    className="p-1 rounded hover:bg-gray-100"
                >
                    <img src={loading} alt="" />
                </button>
            </div>
            )}
    </div>
  );
}
