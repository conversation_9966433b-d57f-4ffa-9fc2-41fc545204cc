import { useState, useRef, useEffect } from 'react';

const ProfileDropdown = () => {
  const [isOpen, setIsOpen] = useState(false);

  // إغلاق القائمة عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);



  return (
    <div className=" bg-gray-100 p-8">

    </div>
  );
};

export default ProfileDropdown;